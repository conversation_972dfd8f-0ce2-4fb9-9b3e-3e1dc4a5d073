#!/usr/bin/env python3
"""
Comprehensive script to fetch users from all Jira instances, get Tempo user IDs,
create proper mappings, and update configuration.
"""

import os
import json
import requests
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Set, Tuple
import sys

def load_config():
    """Load configuration from config.json"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ config.json not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing config.json: {e}")
        sys.exit(1)

def get_jira_users(base_url: str, api_key: str, email: str) -> List[Dict]:
    """Fetch all users from a Jira instance"""
    print(f"🔍 Fetching users from {base_url}...")
    
    headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    users = []
    start_at = 0
    max_results = 50
    
    try:
        while True:
            url = f"{base_url}/rest/api/3/users/search"
            params = {
                'startAt': start_at,
                'maxResults': max_results
            }
            
            response = requests.get(url, headers=headers, auth=(email, api_key), params=params, timeout=15)
            
            if response.status_code == 200:
                batch_users = response.json()
                if not batch_users:
                    break
                    
                users.extend(batch_users)
                start_at += max_results
                
                if len(batch_users) < max_results:
                    break
            else:
                print(f"  ❌ Failed to fetch users: {response.status_code} - {response.text}")
                break
                
    except Exception as e:
        print(f"  ❌ Error fetching users: {e}")
    
    print(f"  ✅ Found {len(users)} users")
    return users

def get_tempo_worklogs(api_key: str, days_back: int = 30) -> List[Dict]:
    """Fetch Tempo worklogs to discover active users"""
    print(f"🔍 Fetching Tempo worklogs for last {days_back} days...")
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days_back)
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    params = {
        'from': start_date.strftime('%Y-%m-%d'),
        'to': end_date.strftime('%Y-%m-%d'),
        'limit': 1000
    }
    
    try:
        url = "https://api.tempo.io/4/worklogs"
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            worklogs = data.get('results', [])
            print(f"  ✅ Found {len(worklogs)} worklogs")
            return worklogs
        else:
            print(f"  ❌ Failed to fetch worklogs: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"  ❌ Error fetching worklogs: {e}")
        return []

def extract_tempo_users(worklogs: List[Dict]) -> Dict[str, Dict]:
    """Extract unique Tempo users from worklogs"""
    users = {}
    
    for worklog in worklogs:
        author = worklog.get('author', {})
        account_id = author.get('accountId')
        display_name = author.get('displayName', 'Unknown')
        
        if account_id and account_id not in users:
            users[account_id] = {
                'account_id': account_id,
                'display_name': display_name,
                'worklog_count': 0,
                'total_hours': 0,
                'recent_activity': []
            }
        
        if account_id:
            users[account_id]['worklog_count'] += 1
            users[account_id]['total_hours'] += worklog.get('timeSpentSeconds', 0) / 3600
            
            # Track recent activity
            if len(users[account_id]['recent_activity']) < 5:
                issue = worklog.get('issue', {})
                users[account_id]['recent_activity'].append({
                    'date': worklog.get('startDate'),
                    'issue': issue.get('key', 'Unknown'),
                    'description': worklog.get('description', '')[:50]
                })
    
    # Round hours
    for user_data in users.values():
        user_data['total_hours'] = round(user_data['total_hours'], 2)
    
    return users

def match_jira_to_tempo_users(jira_users: List[Dict], tempo_users: Dict[str, Dict]) -> Dict[str, Dict]:
    """Match Jira users to Tempo users by account ID"""
    matched_users = {}
    
    # Create lookup by account ID
    jira_lookup = {user['accountId']: user for user in jira_users}
    
    for account_id, tempo_data in tempo_users.items():
        if account_id in jira_lookup:
            jira_user = jira_lookup[account_id]
            matched_users[account_id] = {
                'account_id': account_id,
                'display_name': jira_user.get('displayName', 'Unknown'),
                'email': jira_user.get('emailAddress', ''),
                'active': jira_user.get('active', True),
                'tempo_activity': tempo_data
            }
    
    return matched_users

def suggest_developer_mappings(matched_users: Dict[str, Dict]) -> Dict[str, str]:
    """Suggest developer name mappings based on display names and activity"""
    mappings = {}
    
    # Known developer patterns from your worklogs
    known_patterns = {
        'ivan': 'Ivan Kosovan',
        'kosovan': 'Ivan Kosovan',
        'rostyslav': 'Rostyslav Havryliuk',
        'havryliuk': 'Rostyslav Havryliuk',
        'roman': 'Roman Rasenko',
        'rasenko': 'Roman Rasenko',
        'ed': 'Ed Sokolov',
        'sokolov': 'Ed Sokolov',
        'dmytro': 'Dmytro Marianchenko',
        'marianchenko': 'Dmytro Marianchenko',
        'dmitro': 'Dmitro Zarutskyi',
        'zarutskyi': 'Dmitro Zarutskyi',
        'andrii': 'Andrii Korbutiak',
        'korbutiak': 'Andrii Korbutiak'
    }
    
    for account_id, user_data in matched_users.items():
        display_name = user_data['display_name'].lower()
        
        # Try to match against known patterns
        for pattern, mapped_name in known_patterns.items():
            if pattern in display_name:
                mappings[account_id] = mapped_name
                break
        
        # If no match found, use display name
        if account_id not in mappings:
            mappings[account_id] = user_data['display_name']
    
    return mappings

def update_config_with_mappings(config: Dict, all_mappings: Dict[str, Dict]) -> Dict:
    """Update configuration with new mappings"""
    if 'developer_mapping' not in config:
        config['developer_mapping'] = {}
    if 'tempo' not in config['developer_mapping']:
        config['developer_mapping']['tempo'] = {}
    
    # Update tempo mappings
    existing_tempo = config['developer_mapping']['tempo']
    new_mappings_count = 0
    
    for instance_name, mappings in all_mappings.items():
        for account_id, developer_name in mappings.items():
            if account_id not in existing_tempo:
                existing_tempo[account_id] = developer_name
                new_mappings_count += 1
            else:
                # Update if different
                if existing_tempo[account_id] != developer_name:
                    print(f"  📝 Updating mapping: {account_id} from '{existing_tempo[account_id]}' to '{developer_name}'")
                    existing_tempo[account_id] = developer_name
    
    print(f"✅ Added {new_mappings_count} new Tempo mappings")
    return config

def main():
    print("🚀 Comprehensive User Mapping Script")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    projects = config.get('projects', [])
    
    all_jira_users = {}
    all_tempo_users = {}
    all_mappings = {}
    
    # Process each project
    for project in projects:
        if not project.get('enabled', True):
            continue
            
        project_name = project['name']
        jira_config = project.get('jira')
        tempo_config = project.get('tempo')
        
        print(f"\n📋 Processing project: {project_name}")
        
        # Get Jira users
        if jira_config:
            base_url = jira_config['base_url']
            api_key_env = jira_config['api_key'].strip('${}')
            email_env = jira_config['email'].strip('${}')
            
            api_key = os.getenv(api_key_env)
            email = os.getenv(email_env)
            
            if api_key and email:
                jira_users = get_jira_users(base_url, api_key, email)
                all_jira_users[project_name] = jira_users
            else:
                print(f"  ⚠️ Missing Jira credentials for {project_name}")
        
        # Get Tempo users
        if tempo_config:
            tempo_key_env = tempo_config['api_key'].strip('${}')
            tempo_key = os.getenv(tempo_key_env)
            
            if tempo_key:
                tempo_worklogs = get_tempo_worklogs(tempo_key, days_back=60)
                tempo_users = extract_tempo_users(tempo_worklogs)
                all_tempo_users[project_name] = tempo_users
            else:
                print(f"  ⚠️ Missing Tempo API key for {project_name}")
    
    # Match and create mappings
    print(f"\n🔗 Creating user mappings...")
    
    for project_name in all_jira_users.keys():
        if project_name in all_tempo_users:
            jira_users = all_jira_users[project_name]
            tempo_users = all_tempo_users[project_name]
            
            matched_users = match_jira_to_tempo_users(jira_users, tempo_users)
            mappings = suggest_developer_mappings(matched_users)
            
            all_mappings[project_name] = mappings
            
            print(f"\n📊 {project_name} - Found {len(matched_users)} active users:")
            for account_id, user_data in matched_users.items():
                tempo_activity = user_data['tempo_activity']
                mapped_name = mappings.get(account_id, 'Unknown')
                print(f"  • {user_data['display_name']} → {mapped_name}")
                print(f"    Account ID: {account_id}")
                print(f"    Activity: {tempo_activity['worklog_count']} worklogs, {tempo_activity['total_hours']}h")
                print(f"    Email: {user_data['email']}")
                print()
    
    # Update configuration
    if all_mappings:
        print("📝 Updating configuration...")
        updated_config = update_config_with_mappings(config, all_mappings)
        
        # Save updated configuration
        try:
            with open('config.json', 'w') as f:
                json.dump(updated_config, f, indent=2)
            print("✅ Configuration updated successfully")
        except Exception as e:
            print(f"❌ Error saving configuration: {e}")
    else:
        print("⚠️ No mappings to update")
    
    print(f"\n🎯 Summary:")
    print(f"  • Processed {len(all_jira_users)} Jira instances")
    print(f"  • Found {sum(len(users) for users in all_tempo_users.values())} active Tempo users")
    print(f"  • Created mappings for {sum(len(mappings) for mappings in all_mappings.values())} users")

if __name__ == "__main__":
    main()
