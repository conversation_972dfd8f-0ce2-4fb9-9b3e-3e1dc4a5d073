{"ai": {"provider": "openrouter", "enabled": true, "openrouter": {"api_key": "${OPENROUTER_API_KEY}", "base_url": "https://openrouter.ai/api/v1", "model": "openai/gpt-3.5-turbo", "max_tokens": 1500, "temperature": 0.7}}, "slack": {"token": "${SLACK_TOKEN}", "channel_id": "C09ANDRBK7C", "enabled": true, "require_ai_success": true}, "projects": [{"name": "Childfree Legacy", "enabled": true, "git": {"repository_url": "**************:ChildfreeLegacyOrg/Childfree_Legacy.git", "local_path": "./repositories/childfree_legacy"}, "jira": {"base_url": "https://childfreelegacy.atlassian.net", "api_key": "${JIRA_API_KEY}", "email": "${JIRA_EMAIL}", "project_key": "CHIL", "board_id": 2}, "tempo": {"api_key": "${TEMPO_API_KEY}"}}, {"name": "ConvX XDP", "enabled": true, "git": {"repository_url": "**************:convx-com/xdp.git", "local_path": "./repositories/convx_xdp"}, "jira": {"base_url": "https://convx.atlassian.net", "api_key": "${JIRA_API_KEY}", "email": "${JIRA_EMAIL}", "project_key": "XDPA", "board_id": 67}, "tempo": {"api_key": "${TEMPO_CONVX_API_KEY}"}}, {"name": "TopProperty", "enabled": true, "git": {"repositories": [{"repository_url": "**************:TopPropertyeco/topproperty-api.git", "local_path": "./repositories/topproperty_api"}, {"repository_url": "**************:TopPropertyeco/topproperty-dashboard.git", "local_path": "./repositories/topproperty_dashboard"}, {"repository_url": "**************:TopPropertyeco/topproperty.git", "local_path": "./repositories/topproperty_main"}]}, "jira": {"base_url": "https://aleannlab-team.atlassian.net", "api_key": "${JIRA_API_KEY}", "email": "${JIRA_EMAIL}", "project_key": "SCRUM", "board_id": 1}, "tempo": {"api_key": "${TEMPO_TOPPROPERTY_API_KEY}"}}, {"name": "Research LS", "enabled": true, "git": {"repository_url": "**************:madilyani/research.git", "local_path": "./repositories/research_ls"}, "jira": {"base_url": "https://aleannlab.atlassian.net", "api_key": "${JIRA_ALEANNLAB_API_KEY}", "email": "${JIRA_ALEANNLAB_EMAIL}", "project_key": "LS", "board_id": 35}, "tempo": {"api_key": "${TEMPO_ALEANNLAB_API_KEY}"}}, {"name": "Talent", "enabled": true, "git": {"repository_url": "**************:AleannLab/The-talent-point-frontend.git", "local_path": "./repositories/talent_point_frontend"}, "jira": {"base_url": "https://aleannlab.atlassian.net", "api_key": "${JIRA_ALEANNLAB_API_KEY}", "email": "${JIRA_ALEANNLAB_EMAIL}", "project_key": "TALENT", "board_id": 165}, "tempo": {"api_key": "${TEMPO_ALEANNLAB_API_KEY}"}}, {"name": "LK King", "enabled": true, "git": {"repository_url": "**************:thestinkmaster/lc-app.git", "local_path": "./repositories/lc_app"}, "jira": {"base_url": "https://aleannlab.atlassian.net", "api_key": "${JIRA_ALEANNLAB_API_KEY}", "email": "${JIRA_ALEANNLAB_EMAIL}", "project_key": "LK", "board_id": 99}, "tempo": {"api_key": "${TEMPO_ALEANNLAB_API_KEY}"}}, {"name": "Pixicard", "enabled": true, "git": {"repository_url": "**************:AleannLab/pixicard.git", "local_path": "./repositories/pixicard"}, "jira": null, "tempo": null}, {"name": "<PERSON>", "enabled": true, "git": {"repository_url": "**************:AleannLab/math-lesson.git", "local_path": "./repositories/math_lesson"}, "jira": null, "tempo": null}, {"name": "<PERSON><PERSON>", "enabled": true, "git": {"repository_url": "**************:AleannLab/persone-capsule.git", "local_path": "./repositories/persone_capsule"}, "jira": null, "tempo": null}], "developer_mapping": {"git": {"rostyslf": "Rostyslav F", "dmytro-haidamachenko": "<PERSON><PERSON><PERSON>", "dmytro-marjan": "<PERSON><PERSON><PERSON>", "dmytro-perepelytsa": "<PERSON><PERSON><PERSON>", "ivan-d": "<PERSON>", "maxim-sidorenko": "<PERSON>", "oleksiistupak": "<PERSON><PERSON><PERSON>", "dhaidamachenko1": "<PERSON><PERSON><PERSON>", "Ros": "Rostyslav F", "ivanDAleannlab": "<PERSON>", "maxims": "<PERSON>", "Oleksii": "<PERSON><PERSON><PERSON>", "Dmytro": "<PERSON><PERSON><PERSON>", "dimamarjanAL": "<PERSON><PERSON><PERSON>", "Viacheslav": "<PERSON><PERSON><PERSON>", "Dmytro Perepelytsa": "<PERSON><PERSON><PERSON>", "ivan-kosovan": "<PERSON>", "rostyslav-havryliuk": "<PERSON><PERSON><PERSON><PERSON>", "roman-rasenko": "<PERSON>", "ed-sokolov": "<PERSON>", "dmytro-marianchenko": "<PERSON><PERSON><PERSON>", "dmitro-zarutskyi": "<PERSON><PERSON><PERSON>", "andrii-korbutiak": "<PERSON><PERSON><PERSON>"}, "tempo": {"712020:793f4892-002c-41b1-a76e-3c49cc7fe9a0": "<PERSON><PERSON><PERSON>", "712020:377d65f5-05ae-4fe5-a3bc-e6dec40d9a93": "<PERSON>", "712020:e31b568e-f6ab-4305-af02-41458cf57eb6": "<PERSON>", "712020:3632f7e4-81bb-4243-94f5-ffe0caa8b54e": "<PERSON>", "712020:a4427da4-b19e-4363-923f-ebf7c366b7d4": "<PERSON><PERSON><PERSON>", "712020:a2de72f2-3d45-4d49-ae95-d8c9387f8a1f": "<PERSON><PERSON><PERSON>", "63317599140ba0bf651afd28": "<PERSON><PERSON><PERSON>", "712020:1121b9f2-8c49-49c8-82e8-1777cd82d1bb": "<PERSON><PERSON><PERSON>", "712020:86a1e255-adf5-45e4-94d7-2980cd431d42": "<PERSON>", "712020:6b6fd944-4b63-4570-b57e-927150518fc0": "<PERSON><PERSON><PERSON><PERSON>", "712020:d3c698a3-6d33-4cd1-8650-aac7b9469b39": "<PERSON><PERSON><PERSON>", "712020:6079b8ff-dd79-40b3-8c6e-0cd81b603b64": "AleannLab Developer 7"}}, "git": {"skip_bots": ["*bot*", "gpt-engineer-app[bot]", "dependabot*", "mad<PERSON><PERSON>", "renovate*"], "skip_users": []}, "report": {"schedule": "09:00", "timezone": "UTC", "analyze_days": 1, "default_days_back": 1, "combine_developers": true, "save_local_reports": true, "reports_directory": "./reports"}, "logging": {"level": "DEBUG", "file": "./logs/dev_analyzer.log", "max_file_size": "10MB", "backup_count": 5}}